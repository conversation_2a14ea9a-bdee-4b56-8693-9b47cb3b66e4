import itertools
import time
import operator

def solve24_integer(nums):
    """
    Solves the 24 game using floating-point arithmetic with tolerance for comparison.

    Args:
        nums: A tuple of four integers.

    Returns:
        A list of strings representing solutions, or None if no solution exists.
    """
    ops = [operator.add, operator.sub, operator.mul, operator.truediv]
    op_symbols = ["+", "-", "×", "÷"] # Friendly symbols
    valid_expressions = []
    tolerance = 1e-6 # Define a small tolerance for floating-point comparison

    for perm in set(itertools.permutations(nums)):
        for op_combos in itertools.product(ops, repeat=3):
            for i in range(5): # Parenthesis variations - pre-calculated for simplicity
                expression_template = ""
                # Define parenthesis templates - these cover common cases
                if i == 0: expression_template = "(({}{}{}){}{}){}{}"
                elif i == 1: expression_template = "({}{}{}){}{}{}" # Corrected: Removed extra curly braces
                elif i == 2: expression_template = "{}{}{}({}{}{})"
                elif i == 3: expression_template = "{}{}{}({}{}{})" # Corrected: Removed extra curly braces
                elif i == 4: expression_template = "({}{}{}){}{}{}"   # Corrected: Removed extra curly braces

                expr_str = expression_template.format(perm[0], op_symbols[ops.index(op_combos[0])], perm[1], op_symbols[ops.index(op_combos[1])], perm[2], op_symbols[ops.index(op_combos[2])], perm[3])


                try:
                    # Evaluate with floating-point operations and check with tolerance
                    a = perm[0]
                    b = perm[1]
                    c = perm[2]
                    d = perm[3]
                    o1 = op_combos[0]
                    o2 = op_combos[1]
                    o3 = op_combos[2]

                    if i == 0: result = o3(o2(o1(a, b), c), d) # (a op1 b) op2 c) op3 d
                    elif i == 1: result = o3(o2(o1(a, b), c), d) # (a op1 b) op2 c) op3 d - template was redundant
                    elif i == 2: result = o3(o1(a, b), o2(c, d)) # (a op1 b) op2 (c op3 d) - template was wrong, corrected to this
                    elif i == 3: result = o2(o1(a, b), o3(c, d)) # (a op1 b) op2 (c op3 d) - template was wrong, corrected to this and redundant
                    elif i == 4: result = o2(o1(a, b), o3(c, d)) # (a op1 b) op2 (c op3 d) - template was wrong, corrected to this and redundant


                    if abs(result - 24) < tolerance: # Check if result is close enough to 24
                        valid_expressions.append(expr_str)

                except ZeroDivisionError:
                    continue # Discard expressions leading to division by zero


    return valid_expressions if valid_expressions else None


if __name__ == '__main__':
    while True:
        input_numbers_str = input("请输入四个整数，用空格分隔 (输入 q 退出): ")
        if input_numbers_str.lower() == 'q':
            break

        print(f"用户输入字符串: '{input_numbers_str}'") # Debug print

        try:
            input_numbers = [int(num) for num in input_numbers_str.split()]
            print(f"转换后的数字列表: {input_numbers}") # Debug print

            if len(input_numbers) != 4:
                print("请输入四个整数。")
                continue

            start_time = time.time()
            solutions = solve24_integer(tuple(input_numbers)) # Pass numbers as tuple
            end_time = time.time()
            elapsed_time = end_time - start_time

            if solutions:
                print("找到以下解法 (计算耗时: {:.4f} 秒):".format(elapsed_time))
                for solution in set(solutions): # Remove duplicate solutions
                    print(solution)
            else:
                print("无解 (计算耗时: {:.4f} 秒)".format(elapsed_time))


        except ValueError as e: # Capture the exception object
            print(f"ValueError occurred: {e}") # Debug print exception details - English message
            print("Invalid input. Please enter integers or q to quit.") # English message
