<!DOCTYPE html>
<html>
<head>
<title>Rotating 3D Cone</title>
<style>
body {
  margin: 0;
  overflow: hidden;
}
canvas {
  width: 100%;
  height: 100%;
}
</style>
</head>
<body>
<canvas id="coneCanvas"></canvas>
<script>
  const canvas = document.getElementById('coneCanvas');
  const gl = canvas.getContext('webgl'); // Changed from ctx to gl

  // Vertex shader source code
  const vsSource = `
    attribute vec4 aVertexPosition;
    uniform mat4 uModelViewMatrix;
    uniform mat4 uProjectionMatrix;
    void main() {
      gl_Position = uProjectionMatrix * uModelViewMatrix * aVertexPosition;
    }
  `;

  // Fragment shader source code
  const fsSource = `
    void main() {
      gl_FragColor = vec4(1.0, 0.5, 0.0, 1.0); // Orange color
    }
  `;

  // Initialize shaders
  function initShaders(gl, vsSource, fsSource) {
    const vertexShader = gl.createShader(gl.VERTEX_SHADER);
    gl.shaderSource(vertexShader, vsSource);
    gl.compileShader(vertexShader);

    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
    gl.shaderSource(fragmentShader, fsSource);
    gl.compileShader(fragmentShader);

    const shaderProgram = gl.createProgram();
    gl.attachShader(shaderProgram, vertexShader);
    gl.attachShader(shaderProgram, fragmentShader);
    gl.linkProgram(shaderProgram);

    return shaderProgram;
  }

  // Cone parameters
  const coneHeight = 1.0;
  const coneRadius = 0.5;
  const coneSegments = 36;

  // Create cone vertices
  function createConeVertices() {
    let vertices = [];
    let angleIncrement = 2 * Math.PI / coneSegments;

    // Cone top vertex
    vertices.push(0, coneHeight / 2, 0);

    // Cone base vertices
    for (let i = 0; i <= coneSegments; i++) {
      let angle = i * angleIncrement;
      let x = coneRadius * Math.cos(angle);
      let z = coneRadius * Math.sin(angle);
      vertices.push(x, -coneHeight / 2, z);
    }

    return vertices;
  }

  // Create cone indices
  function createConeIndices() {
    let indices = [];

    // Top triangles
    for (let i = 1; i <= coneSegments; i++) {
      indices.push(0, i, i + 1);
    }
    indices.push(0, coneSegments + 1, 1); // Close the circle

    return indices;
  }

  // Initialize buffers
  function initBuffers(gl, vertices, indices) {
    const vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(vertices), gl.STATIC_DRAW);

    const indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(indices), gl.STATIC_DRAW);

    return {
      vertexBuffer: vertexBuffer,
      indexBuffer: indexBuffer,
      vertexCount: indices.length,
    };
  }

  // Draw the scene
  function drawScene(gl, programInfo, buffers, deltaTime) {
    gl.clearColor(0.0, 0.0, 0.0, 1.0);
    gl.clearDepth(1.0);
    gl.enable(gl.DEPTH_TEST);
    gl.depthFunc(gl.LEQUAL);

    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

    const fieldOfView = 45 * Math.PI / 180;
    const aspect = gl.canvas.clientWidth / gl.canvas.clientHeight;
    const zNear = 0.1;
    const zFar = 100.0;
    const projectionMatrix = mat4.create();

    mat4.perspective(projectionMatrix, fieldOfView, aspect, zNear, zFar);

    const modelViewMatrix = mat4.create();
    mat4.translate(modelViewMatrix, modelViewMatrix, [-0.0, 0.0, -6.0]);
    mat4.rotate(modelViewMatrix, modelViewMatrix, cubeRotation, [0, 1, 0]);

    {
      const numComponents = 3;
      const type = gl.FLOAT;
      const normalize = false;
      const stride = 0;
      const offset = 0;
      gl.bindBuffer(gl.ARRAY_BUFFER, buffers.vertexBuffer);
      gl.vertexAttribPointer(
          programInfo.attribLocations.vertexPosition,
          numComponents,
          type,
          normalize,
          stride,
          offset);
      gl.enableVertexAttribArray(programInfo.attribLocations.vertexPosition);
    }

    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, buffers.indexBuffer);
    gl.useProgram(programInfo.program);

    gl.uniformMatrix4fv(
        programInfo.uniformLocations.projectionMatrix,
        false,
        projectionMatrix);
    gl.uniformMatrix4fv(
        programInfo.uniformLocations.modelViewMatrix,
        false,
        modelViewMatrix);

    {
      const vertexCount = buffers.vertexCount;
      const type = gl.UNSIGNED_SHORT;
      const offset = 0;
      gl.drawElements(gl.TRIANGLES, vertexCount, type, offset);
    }

    cubeRotation += deltaTime;
  }

  // Initialize WebGL
  function initWebGL(canvas) {
    let gl = canvas.getContext('webgl');
    if (gl === null) {
      alert("Unable to initialize WebGL.");
      return null;
    }
    return gl;
  }

  // Main function
  function main() {
    let gl = initWebGL(canvas);
    if (!gl) return;

    const shaderProgram = initShaders(gl, vsSource, fsSource);
    const vertices = createConeVertices();
    const indices = createConeIndices();
    const buffers = initBuffers(gl, vertices, indices);

    const programInfo = {
      program: shaderProgram,
      attribLocations: {
        vertexPosition: gl.getAttribLocation(shaderProgram, 'aVertexPosition'),
      },
      uniformLocations: {
        projectionMatrix: gl.getUniformLocation(shaderProgram, 'uProjectionMatrix'),
        modelViewMatrix: gl.getUniformLocation(shaderProgram, 'uModelViewMatrix'),
      },
    };

    let then = 0;
    function render(now) {
      now *= 0.001;
      const deltaTime = now - then;
      then = now;

      drawScene(gl, programInfo, buffers, deltaTime);
      requestAnimationFrame(render);
    }
    requestAnimationFrame(render);
  }

  let cubeRotation = 0.0;
  let mat4 = glMatrix.mat4;

  main();
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gl-matrix/2.8.1/gl-matrix-min.js" integrity="sha512-zhHQRCI3mZawCZzg4PhvmYyiBYyNP5NWnnUJUghg5w7PopX6LnmKSnShhZFGwbjk/nA/zwHZ9iz5KY5vnlBJYw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
</body>
</html>
