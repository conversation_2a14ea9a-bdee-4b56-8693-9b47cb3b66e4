const boardSize = 10;
const minePercentage = 0.3; // 30% of the grid
let board = [];
let minePositions = [];
let firstClick = true;

function initGame() {
    board = Array.from({ length: boardSize }, () => Array.from({ length: boardSize }, () => ({ revealed: false, mine: false, adjacentMines: 0 })));
    renderBoard();
}

function generateMines(excludeX, excludeY) {
    const mineCount = Math.floor(boardSize * boardSize * minePercentage);
    let positions = new Set();
    while (positions.size < mineCount) {
        const x = Math.floor(Math.random() * boardSize);
        const y = Math.floor(Math.random() * boardSize);
        const posKey = `${x},${y}`;
        if ((x !== excludeX || y !== excludeY) && !positions.has(posKey)) {
            positions.add(posKey);
            board[x][y].mine = true;
        }
    }
    minePositions = Array.from(positions).map(pos => {
        const [x, y] = pos.split(',').map(Number);
        return { x, y };
    });
    calculateAdjacentMines();
}

function calculateAdjacentMines() {
    minePositions.forEach(({ x, y }) => {
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                const nx = x + dx;
                const ny = y + dy;
                if (nx >= 0 && nx < boardSize && ny >= 0 && ny < boardSize && !board[nx][ny].mine) {
                    board[nx][ny].adjacentMines++;
                }
            }
        }
    });
}

function renderBoard() {
    const gameBoard = document.getElementById('game-board');
    gameBoard.innerHTML = '';
    board.forEach((row, x) => {
        row.forEach((cell, y) => {
            const cellElement = document.createElement('div');
            cellElement.classList.add('cell');
            if (cell.revealed) {
                cellElement.classList.add('revealed');
                if (cell.mine) {
                    cellElement.classList.add('mine');
                    cellElement.textContent = '💣';
                } else if (cell.adjacentMines > 0) {
                    cellElement.textContent = cell.adjacentMines;
                }
            }
            cellElement.addEventListener('click', () => revealCell(x, y));
            gameBoard.appendChild(cellElement);
        });
    });
}

function revealCell(x, y) {
    if (firstClick) {
        generateMines(x, y);
        firstClick = false;
    }
    if (board[x][y].revealed) return;
    board[x][y].revealed = true;
    if (board[x][y].mine) {
        alert('Game Over!');
        revealAllMines();
    } else if (board[x][y].adjacentMines === 0) {
        revealAdjacentCells(x, y);
    }
    renderBoard();
}

function revealAdjacentCells(x, y) {
    for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
            const nx = x + dx;
            const ny = y + dy;
            if (nx >= 0 && nx < boardSize && ny >= 0 && ny < boardSize && !board[nx][ny].revealed) {
                revealCell(nx, ny);
            }
        }
    }
}

function revealAllMines() {
    minePositions.forEach(({ x, y }) => {
        board[x][y].revealed = true;
    });
    renderBoard();
}

initGame(); 