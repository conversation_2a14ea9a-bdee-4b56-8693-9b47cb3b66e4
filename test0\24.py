import itertools
import time

def find_24_combinations_integer(numbers):
    """
    使用加减乘除和括号，排列组合4个数字，寻找结果为24的组合 (只输出整数结果)。

    Args:
        numbers: 包含四个整数的列表或元组。

    Returns:
        如果找到结果为24的组合，则返回一个包含所有组合方式的列表。
        如果没有找到，则返回 "无解"。
    """
    operators = ['+', '-', '*', '/']
    solutions = []

    for nums in set(itertools.permutations(numbers)): # 使用set去除数字重复的排列
        for ops in itertools.product(operators, repeat=3):
            # 尝试不同的括号组合
            expressions = [
                f"({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}{ops[2]}{nums[3]}",
                f"({nums[0]}{ops[0]}{nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]}",
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]}",
                f"{nums[0]}{ops[0]}{nums[1]}{ops[1]}({nums[2]}{ops[2]}{nums[3]})",
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]}{ops[2]}{nums[3]})",
                f"({nums[0]}{ops[0]}{nums[1]}){ops[1]}({nums[2]}{ops[2]}{nums[3]})",
                f"(({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}){ops[2]}{nums[3]}",
                f"({nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]})){ops[2]}{nums[3]}",
                f"{nums[0]}{ops[0]}(({nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]})",
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}({nums[2]}{ops[2]}{nums[3]}))",

                f"({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}{ops[2]}{nums[3]}", # Redundant but to ensure coverage
                f"({nums[0]}{ops[0]}{nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]}", # Redundant but to ensure coverage
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]}", # Redundant but to ensure coverage
                f"{nums[0]}{ops[0]}{nums[1]}{ops[1]}({nums[2]}{ops[2]}{nums[3]})", # Redundant but to ensure coverage
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]}{ops[2]}{nums[3]})", # Redundant but to ensure coverage
                f"({nums[0]}{ops[0]}{nums[1]}){ops[1]}({nums[2]}{ops[2]}{nums[3]})", # Redundant but to ensure coverage
                f"(({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}){ops[2]}{nums[3]}", # Redundant but to ensure coverage
                f"({nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]})){ops[2]}{nums[3]}", # Redundant but to ensure coverage
                f"{nums[0]}{ops[0]}(({nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]})", # Redundant but to ensure coverage
                f"{nums[0]}{ops[0]}({nums[1]}{ops[1]}({nums[2]}{ops[2]}{nums[3]}))", # Redundant but to ensure coverage

                f"(({nums[0]}{ops[0]}{nums[1]}{ops[1]}{nums[2]}){ops[2]}{nums[3]})", # More combinations
                f"({nums[0]}{ops[0]}({nums[1]}{ops[1]}{nums[2]}{ops[2]}{nums[3]}))", # More combinations
                f"({nums[0]}{ops[0]}{nums[1]}{ops[1]}({nums[2]}{ops[2]}{nums[3]}))", # More combinations
                f"((({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}){ops[2]}{nums[3]})", # More combinations
                f"(((({nums[0]}{ops[0]}{nums[1]}){ops[1]}{nums[2]}){ops[2]}{nums[3]}))", # Redundant but for completeness
            ]

            for expr in expressions:
                try:
                    # 使用eval计算表达式的值，并转换为浮点数进行除法，但最终检查整数结果
                    result = eval(expr.replace("/", "/")) # 确保使用浮点数除法
                    # Modified condition: Check for integer result after rounding
                    if int(round(result)) == 24: # Check rounded integer value
                        solutions.append(expr.replace("/", "÷").replace("*", "×")) # Store with friendly symbols
                except ZeroDivisionError:
                    # 忽略除以零的错误
                    pass

    if solutions:
        return solutions
    else:
        return "无解"

if __name__ == '__main__':
    while True:
        input_numbers_str = input("请输入四个整数，用空格分隔 (输入 q 退出): ")
        if input_numbers_str.lower() == 'q':
            break  # 输入 q 退出循环

        try:
            input_numbers = [int(num) for num in input_numbers_str.split()] # 转换为整数

            if len(input_numbers) != 4:
                print("请输入四个整数。")
            else:
                start_time = time.time() # 记录开始时间
                results = find_24_combinations_integer(input_numbers) # 调用整数版本函数
                end_time = time.time() # 记录结束时间
                elapsed_time = end_time - start_time # 计算耗时

                if results == "无解":
                    print("无解 (计算耗时: {:.4f} 秒)".format(elapsed_time)) # 显示无解和耗时
                else:
                    print("找到以下整数解法 (计算耗时: {:.4f} 秒):".format(elapsed_time)) # 显示解法和耗时
                    for solution in set(results): # 使用set去除重复的解法
                        print(solution)
        except ValueError:
            print("输入无效，请输入整数或 q 退出。")
