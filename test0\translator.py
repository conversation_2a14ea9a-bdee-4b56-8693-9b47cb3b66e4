import requests
import json
from langdetect import detect

def translate_text(text, access_token):
    """
    Detects the language of the input text and translates it.
    If the text is Chinese, it translates to English.
    Otherwise, it translates to Chinese.

    Args:
        text (str): The text to be translated.
        access_token (str): Your access token for the translation API.

    Returns:
        str: The translated text, or None if translation fails.
    """
    try:
        source_lang = detect(text)
    except:
        source_lang = 'en' # default to English if language detection fails

    if source_lang.startswith('zh'):
        target_lang = 'EN'
        source_lang_api = 'ZH' # Assuming API uses ZH for Chinese
    else:
        target_lang = 'ZH'
        source_lang_api = 'EN' # Assuming API defaults to EN if not Chinese

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {access_token}'
    }
    data = {
        "text": text,
        "source_lang": source_lang_api,
        "target_lang": target_lang
    }

    try:
        response = requests.post('http://localhost:1188/translate', headers=headers, data=json.dumps(data))
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        return response.json().get('translated_text') # Assuming the API response is JSON and contains 'translated_text'
    except requests.exceptions.RequestException as e:
        print(f"Translation failed: {e}")
        return None

if __name__ == "__main__":
    input_text = input("请输入您要翻译的文字: ")
    access_token = "your_access_token" # 请替换成您的实际 access_token
    translated_text = translate_text(input_text, access_token)

    if translated_text:
        print("翻译结果:")
        print(translated_text)