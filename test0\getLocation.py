def address(ip:str)->dict:
    import re
    def gethost(url:str)->str:
      if not re.search(r'/',url) is None:
        from urllib.parse import urlparse
        return urlparse(url).netloc
      else:
        return url

    def getip(host:str)->str:
        import socket
        return socket.gethostbyname(host)

    if not re.search(r'/',ip) is None:
      ip = gethost(ip)
    if not re.search(r'[a-zA-Z]',ip) is None:
      ip = getip(ip)
    return requests.post(ip_url,data={'ip':ip}).json()
