import argparse

def login():
  print("login")

def logout():
  print("logout")


parser=argparse.ArgumentParser()
parser.add_argument("--login",dest="login",action="store_true",help="login TY network")
parser.add_argument("--logout",dest="logout",action="store_true",help="logout TY network")
args=parser.parse_args()
print("args=",args)

print(dir(args))

if args.login:
  
  print("login TY")
elif args.logout:
  print("logout TY")
